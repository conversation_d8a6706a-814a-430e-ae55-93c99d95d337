/**
 * 测试修复效果的工具函数
 */

import { apiClient } from '../services/ApiClient';
import { store } from '../store';
import { setShowConflictPanel, addConflict } from '../store/collaboration/conflictSlice';
import { ConflictType, ConflictStatus } from '../services/ConflictResolutionService';
import { OperationType } from '../services/CollaborationService';

/**
 * 测试API客户端修复
 */
export async function testApiClientFixes(): Promise<boolean> {
  console.log('🔧 测试API客户端修复...');
  
  try {
    // 测试API基础URL获取
    const baseUrl = (apiClient as any).getApiBaseUrl();
    console.log('✅ API基础URL:', baseUrl);
    
    // 测试健康检查
    const isHealthy = await apiClient.checkHealth();
    console.log('✅ API健康检查:', isHealthy ? '通过' : '失败');
    
    // 测试错误处理
    try {
      await apiClient.get('/test-nonexistent-endpoint');
    } catch (error) {
      console.log('✅ 错误处理测试: 正确捕获了404错误');
    }
    
    return true;
  } catch (error) {
    console.error('❌ API客户端测试失败:', error);
    return false;
  }
}

/**
 * 创建测试冲突数据
 */
export function createTestConflict(): void {
  console.log('🔧 创建测试冲突数据...');

  try {
    // 创建一个测试冲突
    const testConflict = {
      id: 'test-conflict-' + Date.now(),
      type: ConflictType.ENTITY_CONFLICT,
      status: ConflictStatus.PENDING,
      localOperation: {
        id: 'local-op-' + Date.now(),
        type: OperationType.ENTITY_UPDATE,
        userId: 'test-user-1',
        timestamp: Date.now(),
        data: {
          entityId: 'test-entity-1',
          property: 'position',
          value: { x: 10, y: 20, z: 30 }
        }
      },
      remoteOperation: {
        id: 'remote-op-' + Date.now(),
        type: OperationType.ENTITY_UPDATE,
        userId: 'test-user-2',
        timestamp: Date.now() + 1000,
        data: {
          entityId: 'test-entity-1',
          property: 'position',
          value: { x: 15, y: 25, z: 35 }
        }
      },
      createdAt: Date.now()
    };

    // 添加冲突到Redux store
    store.dispatch(addConflict(testConflict));
    console.log('✅ 测试冲突已创建:', testConflict.id);
  } catch (error) {
    console.error('❌ 创建测试冲突失败:', error);
  }
}

/**
 * 测试冲突面板关闭功能
 */
export function testConflictPanelClose(): boolean {
  console.log('🔧 测试冲突面板关闭功能...');

  try {
    // 创建测试冲突数据
    createTestConflict();

    // 检查冲突面板是否自动显示
    const showPanelAfterConflict = store.getState().conflict.showConflictPanel;
    console.log('✅ 冲突面板自动显示状态:', showPanelAfterConflict);

    // 手动关闭冲突面板
    store.dispatch(setShowConflictPanel(false));
    const showPanelAfterClose = store.getState().conflict.showConflictPanel;
    console.log('✅ 冲突面板关闭状态:', showPanelAfterClose);

    if (!showPanelAfterClose) {
      console.log('✅ 冲突面板关闭功能正常');
      return true;
    } else {
      console.error('❌ 冲突面板关闭功能异常');
      return false;
    }
  } catch (error) {
    console.error('❌ 冲突面板测试失败:', error);
    return false;
  }
}

/**
 * 测试项目管理错误处理
 */
export function testProjectManagementErrorHandling(): boolean {
  console.log('🔧 测试项目管理错误处理...');
  
  try {
    // 检查是否有未捕获的错误
    let hasUncaughtErrors = false;
    
    const originalConsoleError = console.error;
    console.error = (...args) => {
      if (args.some(arg => typeof arg === 'string' && arg.includes('Uncaught'))) {
        hasUncaughtErrors = true;
      }
      originalConsoleError.apply(console, args);
    };
    
    // 恢复原始console.error
    setTimeout(() => {
      console.error = originalConsoleError;
    }, 1000);
    
    if (!hasUncaughtErrors) {
      console.log('✅ 项目管理错误处理正常');
      return true;
    } else {
      console.error('❌ 检测到未捕获的错误');
      return false;
    }
  } catch (error) {
    console.error('❌ 项目管理错误处理测试失败:', error);
    return false;
  }
}

/**
 * 运行所有测试
 */
export async function runAllTests(): Promise<void> {
  console.log('🚀 开始运行修复效果测试...');
  
  const results = {
    apiClient: false,
    conflictPanel: false,
    projectManagement: false,
  };
  
  // 测试API客户端
  results.apiClient = await testApiClientFixes();
  
  // 测试冲突面板
  results.conflictPanel = testConflictPanelClose();
  
  // 测试项目管理
  results.projectManagement = testProjectManagementErrorHandling();
  
  // 输出测试结果
  console.log('\n📊 测试结果汇总:');
  console.log('API客户端修复:', results.apiClient ? '✅ 通过' : '❌ 失败');
  console.log('冲突面板关闭:', results.conflictPanel ? '✅ 通过' : '❌ 失败');
  console.log('项目管理错误处理:', results.projectManagement ? '✅ 通过' : '❌ 失败');
  
  const allPassed = Object.values(results).every(result => result);
  console.log('\n🎯 总体结果:', allPassed ? '✅ 所有测试通过' : '❌ 部分测试失败');
  
  if (allPassed) {
    console.log('🎉 恭喜！所有修复都已生效，问题已解决。');
  } else {
    console.log('⚠️ 部分问题仍需进一步调试。');
  }
}

/**
 * 在开发环境中自动运行测试
 */
if (process.env.NODE_ENV === 'development') {
  // 延迟运行测试，确保应用完全加载
  setTimeout(() => {
    runAllTests();
  }, 3000);
}
